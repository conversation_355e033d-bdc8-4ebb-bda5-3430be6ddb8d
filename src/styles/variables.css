/* 共享变量定义 */
:root {
    /* === 字体 === */
    --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    --font-size-sm: 12px;
    --font-size-base: 14px;
    --font-size-lg: 16px;
    --font-size-xl: 18px;
    --font-size-xxl: 20px;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --line-height-base: 1.5;

    /* === 颜色 === */
    --color-success: #008e5e;
    --color-error: #EF4444;
    --color-content-invert: #FFFFFF;

    /* === 阴影 === */
    --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-modal: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);

    /* === 圆角 === */
    --radius-xs: 2px;
    --radius-sm: 4px;
    --radius-s: 6px;
    --radius-base: 8px;
    --radius-m: 12px;
    --radius-lg: 16px;
    --radius-full: 9999px;

    /* === 加载组件 === */
    --loading-backdrop: rgba(0, 0, 0, 0.7);
    --loading-container-bg: rgba(0, 0, 0, 0.7);
    --loading-container-backdrop-filter: blur(10px);

    /* === 模型卡片 === */
    --card-border-default: rgba(255, 255, 255, 0.15);
    --card-border-hover: rgba(255, 255, 255, 0.4);
    --card-shadow-inset: 0px 1px 3px 0px rgba(255, 255, 255, 0.15) inset;
    --card-shadow-hover: 0px 1px 3px 0px rgba(255, 255, 255, 0.35) inset, 0 16px 48px rgba(0, 0, 0, 0.2);
}

/* 暗色主题（默认）*/
.theme-dark {
    /* === 品牌色 === */
    --color-brand: #2269EC;
    --color-brand-hover: #174291;
  
    /* === 辅助色 === */
    --color-support: rgba(255, 255, 255, 0.12);
    --color-support-hover: rgba(255, 255, 255, 0.06);
  
    /* === 边框 === */
    --color-border: rgba(255, 255, 255, 0.08);
    --color-divider: #000000;
  
    /* === 内容颜色 === */
    --color-content-accent: rgba(255, 255, 255, 0.9);
    --color-content-regular: rgba(255, 255, 255, 0.7);
    --color-content-secondary: rgba(255, 255, 255, 0.3);
    --color-content-mute: rgba(255, 255, 255, 0.35);
  
    /* === 背景 === */
    --color-bg-page: #1A1A1A;
    --color-bg-dialog: #2e2e2e;
    --color-bg-primary: #272727;
    --color-bg-overlay: rgba(255, 255, 255, 0.08);
    --color-bg-hover: rgba(255, 255, 255, 0.05);
}